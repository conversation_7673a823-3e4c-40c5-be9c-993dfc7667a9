"""
Constants used throughout the application.
"""
import flet as ft
from facial_recognition_system.config import Config

# Application information
APP_NAME = Config.APP_NAME
APP_VERSION = Config.VERSION

# UI constants
WINDOW_WIDTH = Config.WINDOW_WIDTH
WINDOW_HEIGHT = Config.WINDOW_HEIGHT
CARD_WIDTH = 600
FORM_WIDTH = 400

# Routes
ROUTE_START = "/"
ROUTE_DASHBOARD = "/dashboard"
ROUTE_CLASSES = "/classes"
ROUTE_SUBJECTS = "/subjects"
ROUTE_QUIZZES = "/quizzes"
ROUTE_ADMIN = "/admin"
ROUTE_SETTINGS = "/settings"
ROUTE_STUDENT_DETAILS = "/student"

# Colors (using <PERSON><PERSON>'s color system)
COLOR_PRIMARY = "primary"
COLOR_ERROR = "error"
COLOR_SUCCESS = "green"
COLOR_WARNING = "amber"
COLOR_INFO = "blue"

# Icons
ICON_DASHBOARD = ft.Icons.DASHBOARD
ICON_CLASS = ft.Icons.CLASS_
ICON_SUBJECT = ft.Icons.BOOK
ICON_QUIZ = ft.Icons.QUIZ
ICON_ATTENDANCE = ft.Icons.FACT_CHECK
ICON_ATTENDANCE_HISTORY = ft.Icons.HISTORY
ICON_ADD = ft.Icons.ADD
ICON_EDIT = ft.Icons.EDIT
ICON_DELETE = ft.Icons.DELETE
ICON_SAVE = ft.Icons.SAVE
ICON_CANCEL = ft.Icons.CANCEL
ICON_BACK = ft.Icons.ARROW_BACK
ICON_SETTINGS = ft.Icons.SETTINGS
ICON_LIGHT_MODE = ft.Icons.LIGHT_MODE
ICON_DARK_MODE = ft.Icons.DARK_MODE
ICON_PERSON = ft.Icons.PERSON
ICON_PERSON_ADD = ft.Icons.PERSON_ADD
ICON_MENU = ft.Icons.MENU
ICON_MORE = ft.Icons.MORE_VERT
ICON_FACE = ft.Icons.FACE
ICON_SEARCH = ft.Icons.SEARCH
ICON_CLEAR = ft.Icons.CLEAR
ICON_ADMIN = ft.Icons.ADMIN_PANEL_SETTINGS
